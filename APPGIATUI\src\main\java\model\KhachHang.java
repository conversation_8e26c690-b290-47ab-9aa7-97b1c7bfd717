/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package model;


import java.sql.Date;
/**
 *
 * 
 */
public class KhachHang {
        private int id;
    private String ten;
    private String sdt;
    private String diachi;

    // Constructor
    public KhachHang(int id, String ten, String sdt, String diachi) {
        this.id = id;
        this.ten = ten;
        this.sdt = sdt;
        this.diachi = diachi;
    }

    // Getter và Setter (bạn có thể thêm nếu dùng JTable)
    public int getId() { return id; }
    public String getTen() { return ten; }
    public String getSdt() { return sdt; }
    public String getDiachi() { return diachi; }

    @Override
    public String toString() {
        return id + " - " + ten + " - " + sdt + " - " + diachi;
    }
}
