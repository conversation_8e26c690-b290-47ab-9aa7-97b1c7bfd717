/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package util;



import java.sql.Connection;
import java.sql.DriverManager;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class DBConnection {
    // Cấu hình kết nối SQL Server - bạn có thể thay đổi theo môi trường của mình
    static String url = "***********************************************************************************************************";
    static String user = "sa";
    static String pass = "sa";
    
    public static Connection getConnection()
    {
        Connection conn = null;
        try {
            // 1. Nạp driver SQL Server
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
            System.out.println("✅ Driver SQL Server đã được nạp thành công");

            // 2. Thử kết nối CSDL
            System.out.println("🔄 Đang kết nối tới: " + url);
            System.out.println("👤 User: " + user);

            conn = DriverManager.getConnection(url, user, pass);

            if (conn != null) {
                System.out.println("✅ Kết nối database thành công!");
            }

        } catch (ClassNotFoundException e) {
            System.err.println("❌ Không tìm thấy SQL Server JDBC Driver!");
            System.err.println("Hãy kiểm tra dependency trong pom.xml");
            e.printStackTrace();
        } catch (Exception ex) {
            System.err.println("❌ Lỗi kết nối database: " + ex.getMessage());
            System.err.println("Kiểm tra:");
            System.err.println("1. SQL Server có đang chạy không?");
            System.err.println("2. Database 'QLGU' có tồn tại không?");
            System.err.println("3. Username/password có đúng không?");
            System.err.println("4. Port 1433 có mở không?");
            ex.printStackTrace();
        }
        return conn;
    }
    
    public static void main(String[] args) {
        System.out.println(DBConnection.getConnection());
    }
}