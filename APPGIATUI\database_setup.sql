-- Script tạo database và bảng cho ứng dụng Giặt Ủi
-- Chạy script này trong SQL Server Management Studio

-- 1. Tạo database (nếu chưa có)
IF NOT EXISTS (SELECT * FROM sys.databases WHERE name = 'QLGU')
BEGIN
    CREATE DATABASE QLGU;
    PRINT '✅ Database QLGU đã được tạo';
END
ELSE
BEGIN
    PRINT '⚠️ Database QLGU đã tồn tại';
END

-- Sử dụng database QLGU
USE QLGU;

-- 2. T<PERSON><PERSON> b<PERSON><PERSON> (nếu chưa có)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='<PERSON>ha<PERSON>Hang' AND xtype='U')
BEGIN
    CREATE TABLE KhachHang (
        id INT IDENTITY(1,1) PRIMARY KEY,
        ten NVARCHAR(100) NOT NULL,
        sdt VARCHAR(15) NOT NULL,
        diachi NVARCHAR(200) NOT NULL
    );
    PRINT '✅ Bảng KhachHang đã được tạo';
END
ELSE
BEGIN
    PRINT '⚠️ Bảng KhachHang đã tồn tại';
END

-- 3. Thêm dữ liệu mẫu (nếu bảng trống)
IF NOT EXISTS (SELECT * FROM KhachHang)
BEGIN
    INSERT INTO KhachHang (ten, sdt, diachi) VALUES 
    (N'Nguyễn Văn An', '0901234567', N'123 Đường ABC, Quận 1, TP.HCM'),
    (N'Trần Thị Bình', '0912345678', N'456 Đường XYZ, Quận 2, TP.HCM'),
    (N'Lê Văn Cường', '0923456789', N'789 Đường DEF, Quận 3, TP.HCM'),
    (N'Phạm Thị Dung', '0934567890', N'321 Đường GHI, Quận 4, TP.HCM'),
    (N'Hoàng Văn Em', '0945678901', N'654 Đường JKL, Quận 5, TP.HCM');
    
    PRINT '✅ Đã thêm 5 khách hàng mẫu';
END
ELSE
BEGIN
    PRINT '⚠️ Bảng KhachHang đã có dữ liệu';
END

-- 4. Kiểm tra dữ liệu
SELECT COUNT(*) as 'Tổng số khách hàng' FROM KhachHang;
SELECT * FROM KhachHang;

PRINT '🎉 Setup database hoàn tất!';
