import util.DBConnection;
import model.KhachHang;
import model.KhachHangDAO;
import java.sql.Connection;
import java.util.List;

public class TestConnection {
    public static void main(String[] args) {
        System.out.println("=== TEST KẾT NỐI SQL SERVER ===");

        // Bước 1: Test kết nối cơ bản
        System.out.println("\n1. Kiểm tra kết nối database...");
        Connection conn = DBConnection.getConnection();

        if (conn == null) {
            System.out.println("❌ Không thể kết nối database!");
            System.out.println("\n🔧 HƯỚNG DẪN KHẮC PHỤC:");
            System.out.println("1. Kiểm tra SQL Server có đang chạy không");
            System.out.println("2. Kiểm tra SQL Server Authentication mode");
            System.out.println("3. Kiểm tra TCP/IP protocol có enable không");
            System.out.println("4. Kiểm tra port 1433 có mở không");
            System.out.println("5. Tạo database 'QLGU' nếu chưa có");
            return;
        }

        try {
            conn.close();
            System.out.println("✅ Kết nối database OK!");
        } catch (Exception e) {
            System.out.println("⚠️ Lỗi khi đóng kết nối: " + e.getMessage());
        }

        // Bước 2: Test truy vấn dữ liệu
        System.out.println("\n2. Kiểm tra dữ liệu khách hàng...");
        try {
            List<KhachHang> ds = KhachHangDAO.getAllKhachHang();

            if (ds.isEmpty()) {
                System.out.println("⚠️ Không có dữ liệu khách hàng trong database.");
                System.out.println("Hãy kiểm tra:");
                System.out.println("- Bảng 'KhachHang' có tồn tại không?");
                System.out.println("- Bảng có dữ liệu không?");
            } else {
                System.out.println("✅ Tìm thấy " + ds.size() + " khách hàng:");
                for (KhachHang kh : ds) {
                    System.out.println("  " + kh);
                }
            }
        } catch (Exception e) {
            System.out.println("❌ Lỗi khi truy vấn dữ liệu: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println("\n=== KẾT THÚC TEST ===");
    }
}

